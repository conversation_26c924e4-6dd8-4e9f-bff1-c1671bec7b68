#!/usr/bin/env python3
"""
AI-Powered Web Testing System
Free alternative to operative.sh with local LLM integration
Enhanced with intelligent test case generation and analysis
"""

from playwright.sync_api import sync_playwright
import json
import time
import os
import re
from dataclasses import dataclass
from typing import List, Dict, Optional
import subprocess
import requests
import base64

@dataclass
class TestResult:
    test_name: str
    status: str
    details: str
    screenshot_path: str
    timestamp: str

class AITestIntelligence:
    """AI-powered test case generation and analysis"""

    def __init__(self):
        self.ollama_available = self.check_ollama()
        self.fallback_patterns = {
            'email': [
                '<EMAIL>', 'invalid-email', '', 'test@',
                'user@domain', '<EMAIL>'
            ],
            'password': [
                'SecurePass123!', 'weak', '', '123', 'password',
                'VeryLongPasswordThatExceedsNormalLimits123456789!'
            ],
            'text': [
                'Normal text', '<script>alert("xss")</script>', '',
                'Special chars: !@#$%^&*()', 'Very long text ' * 50
            ],
            'number': ['123', '-1', '0', '999999999', 'abc', ''],
            'url': ['https://example.com', 'invalid-url', '', 'ftp://test.com']
        }

    def check_ollama(self):
        """Check if Ollama is available"""
        try:
            response = requests.get('http://localhost:11434/api/tags', timeout=2)
            return response.status_code == 200
        except:
            return False

    def analyze_page_with_ai(self, page_content: str, screenshot_path: str = None):
        """Use AI to analyze page content and generate insights"""
        if self.ollama_available:
            return self._analyze_with_ollama(page_content, screenshot_path)
        else:
            return self._analyze_with_patterns(page_content)

    def _analyze_with_ollama(self, page_content: str, screenshot_path: str = None):
        """Advanced AI analysis using Ollama"""
        try:
            # Prepare the prompt
            prompt = f"""
            Analyze this web page content and provide testing insights:

            HTML Content (first 2000 chars):
            {page_content[:2000]}

            Please analyze and provide:
            1. Potential usability issues
            2. Security concerns
            3. Accessibility problems
            4. Performance bottlenecks
            5. Missing functionality
            6. Recommended test scenarios

            Format as JSON with categories: usability, security, accessibility, performance, missing, tests
            """

            # Call Ollama API
            response = requests.post('http://localhost:11434/api/generate',
                json={
                    'model': 'llama2',
                    'prompt': prompt,
                    'stream': False
                }, timeout=30)

            if response.status_code == 200:
                result = response.json()
                return self._parse_ai_response(result.get('response', ''))

        except Exception as e:
            print(f"🤖 AI analysis failed: {e}")

        return self._analyze_with_patterns(page_content)

    def _analyze_with_patterns(self, page_content: str):
        """Fallback pattern-based analysis"""
        issues = []
        recommendations = []

        # Check for common issues
        if 'password' in page_content.lower() and 'autocomplete' not in page_content.lower():
            issues.append("Password fields missing autocomplete attributes")

        if '<img' in page_content and 'alt=' not in page_content:
            issues.append("Images missing alt attributes")

        if 'form' in page_content.lower() and 'csrf' not in page_content.lower():
            issues.append("Forms may be missing CSRF protection")

        # Generate recommendations
        if 'login' in page_content.lower():
            recommendations.append("Test login with invalid credentials")
            recommendations.append("Test password reset functionality")

        if 'signup' in page_content.lower():
            recommendations.append("Test signup with duplicate email")
            recommendations.append("Test email validation")

        return {
            'usability': issues[:2] if issues else ["No major usability issues detected"],
            'security': issues[2:4] if len(issues) > 2 else ["Basic security checks recommended"],
            'accessibility': issues[4:] if len(issues) > 4 else ["Accessibility review needed"],
            'performance': ["Check image optimization", "Minimize JavaScript bundles"],
            'missing': ["Consider adding loading states", "Add error boundaries"],
            'tests': recommendations if recommendations else ["Test core user flows"]
        }

    def _parse_ai_response(self, response: str):
        """Parse AI response and extract JSON"""
        try:
            # Try to extract JSON from response
            json_match = re.search(r'\{.*\}', response, re.DOTALL)
            if json_match:
                return json.loads(json_match.group())
        except:
            pass

        # Fallback to pattern analysis
        return {
            'usability': ["AI analysis completed"],
            'security': ["Security review recommended"],
            'accessibility': ["Accessibility check needed"],
            'performance': ["Performance optimization suggested"],
            'missing': ["Feature completeness review"],
            'tests': ["Comprehensive testing recommended"]
        }

    def generate_smart_test_cases(self, form_html: str, form_type: str = "unknown"):
        """Generate intelligent test cases based on form analysis"""
        if self.ollama_available:
            return self._generate_with_ai(form_html, form_type)
        else:
            return self._generate_with_patterns(form_html, form_type)

    def _generate_with_ai(self, form_html: str, form_type: str):
        """AI-powered test case generation"""
        try:
            prompt = f"""
            Generate comprehensive test cases for this {form_type} form:

            {form_html}

            Generate test cases for:
            1. Valid inputs (happy path)
            2. Invalid inputs (edge cases)
            3. Security testing (XSS, injection)
            4. Boundary testing (min/max values)
            5. Accessibility testing

            Return as JSON array with: field, value, expected_result, test_type
            """

            response = requests.post('http://localhost:11434/api/generate',
                json={
                    'model': 'llama2',
                    'prompt': prompt,
                    'stream': False
                }, timeout=20)

            if response.status_code == 200:
                result = response.json()
                return self._parse_test_cases(result.get('response', ''))

        except Exception as e:
            print(f"🤖 AI test generation failed: {e}")

        return self._generate_with_patterns(form_html, form_type)

    def _generate_with_patterns(self, form_html: str, form_type: str):
        """Pattern-based test case generation"""
        test_cases = []

        # Detect field types and generate appropriate tests
        for field_type, test_values in self.fallback_patterns.items():
            if field_type in form_html.lower():
                for value in test_values:
                    test_cases.append({
                        'field': field_type,
                        'value': value,
                        'expected_result': 'valid' if value == test_values[0] else 'invalid',
                        'test_type': 'boundary' if len(value) > 50 else 'standard'
                    })

        return test_cases

    def _parse_test_cases(self, response: str):
        """Parse AI response for test cases"""
        try:
            json_match = re.search(r'\[.*\]', response, re.DOTALL)
            if json_match:
                return json.loads(json_match.group())
        except:
            pass

        # Fallback
        return [
            {'field': 'email', 'value': '<EMAIL>', 'expected_result': 'valid', 'test_type': 'happy_path'},
            {'field': 'email', 'value': 'invalid', 'expected_result': 'invalid', 'test_type': 'negative'}
        ]

class AIWebTester:
    def __init__(self, headless: bool = False):
        self.playwright = sync_playwright().start()
        self.browser = self.playwright.chromium.launch(headless=headless)
        self.results: List[TestResult] = []
        self.screenshots_dir = "test-screenshots"
        self.ai_intelligence = AITestIntelligence()
        os.makedirs(self.screenshots_dir, exist_ok=True)

        # Print AI status
        if self.ai_intelligence.ollama_available:
            print("🧠 AI Intelligence: ENABLED (Ollama detected)")
        else:
            print("🧠 AI Intelligence: PATTERN-BASED (Ollama not available)")
    
    def run_comprehensive_test(self, url: str, task: str = "comprehensive testing"):
        """Main testing entry point"""
        print(f"🚀 Starting AI Web Testing for: {url}")
        print(f"📋 Task: {task}")

        page = self.browser.new_page()

        # Enable console logging
        page.on("console", lambda msg: print(f"🖥️ Console [{msg.type}]: {msg.text}"))

        try:
            page.goto(url, wait_until="networkidle")

            # Run all test suites (including AI-powered analysis)
            tests = [
                self.test_page_load,
                self.test_accessibility,
                self.test_forms,
                self.test_navigation,
                self.test_responsive_design,
                self.test_performance,
                self.test_security_basics,
                self.test_user_flows,
                self.test_ai_comprehensive_analysis,
                # New edge case testing methods
                self.test_edge_case_authentication,
                self.test_edge_case_assessment_system,
                self.test_edge_case_data_validation,
                self.test_edge_case_error_handling,
                self.test_edge_case_boundary_conditions,
                self.test_edge_case_concurrent_operations,
                self.test_edge_case_malformed_inputs,
                self.test_edge_case_session_management
            ]

            for test in tests:
                try:
                    print(f"🧪 Running {test.__name__}...")
                    result = test(page)
                    self.results.append(result)
                    print(f"   {'✅' if result.status == 'PASSED' else '❌'} {result.status}: {result.details}")
                except Exception as e:
                    error_result = TestResult(
                        test_name=test.__name__,
                        status="ERROR",
                        details=str(e),
                        screenshot_path=self.take_screenshot(page, f"error_{test.__name__}"),
                        timestamp=self.get_timestamp()
                    )
                    self.results.append(error_result)
                    print(f"   ❌ ERROR: {str(e)}")

        finally:
            page.close()

        return self.generate_report()
    
    def test_page_load(self, page):
        """Test basic page loading"""
        title = page.title()
        url = page.url
        
        issues = []
        if not title or title == "":
            issues.append("Missing page title")
        if "error" in title.lower() or "404" in title:
            issues.append(f"Error page detected: {title}")
        
        status = "FAILED" if issues else "PASSED"
        details = f"Title: '{title}'" + (f" | Issues: {'; '.join(issues)}" if issues else "")
        
        return TestResult("page_load", status, details, "", self.get_timestamp())
    
    def test_accessibility(self, page):
        """Test accessibility compliance"""
        issues = []
        
        # Check for alt tags on images
        images_without_alt = page.query_selector_all('img:not([alt])')
        if images_without_alt:
            issues.append(f"{len(images_without_alt)} images missing alt text")
        
        # Check for form labels
        inputs = page.query_selector_all('input[type="text"], input[type="email"], input[type="password"], textarea')
        unlabeled_inputs = []
        
        for inp in inputs:
            input_id = inp.get_attribute("id")
            has_label = False
            
            if input_id:
                label = page.query_selector(f'label[for="{input_id}"]')
                if label:
                    has_label = True
            
            if not has_label and not inp.get_attribute("aria-label"):
                unlabeled_inputs.append(inp)
        
        if unlabeled_inputs:
            issues.append(f"{len(unlabeled_inputs)} inputs without proper labels")
        
        # Check for heading structure
        headings = page.query_selector_all('h1, h2, h3, h4, h5, h6')
        if not headings:
            issues.append("No heading structure found")
        
        status = "FAILED" if issues else "PASSED"
        details = "; ".join(issues) if issues else "All accessibility checks passed"
        
        return TestResult("accessibility", status, details, "", self.get_timestamp())
    
    def test_forms(self, page):
        """AI-Enhanced form functionality testing"""
        forms = page.query_selector_all('form')

        if not forms:
            return TestResult("forms", "SKIPPED", "No forms found", "", self.get_timestamp())

        issues = []
        tested_forms = 0
        ai_insights = []

        for form_idx, form in enumerate(forms):
            tested_forms += 1

            # Get form HTML for AI analysis
            form_html = form.inner_html()
            form_type = self._detect_form_type(form_html)

            print(f"🤖 Analyzing {form_type} form with AI...")

            # Generate AI-powered test cases
            test_cases = self.ai_intelligence.generate_smart_test_cases(form_html, form_type)

            # Execute AI-generated test cases
            inputs = form.query_selector_all('input, textarea, select')

            for test_case in test_cases[:5]:  # Limit to 5 test cases per form
                matching_inputs = [inp for inp in inputs
                                 if test_case['field'].lower() in (inp.get_attribute('type') or '').lower()
                                 or test_case['field'].lower() in (inp.get_attribute('name') or '').lower()]

                for inp in matching_inputs[:1]:  # Test first matching input
                    try:
                        # Clear and fill with test value
                        inp.fill("")
                        inp.fill(test_case['value'])
                        page.wait_for_timeout(300)

                        # Check for validation messages
                        validation_msg = page.query_selector('.error, .invalid, [role="alert"]')
                        if validation_msg and test_case['expected_result'] == 'valid':
                            issues.append(f"Unexpected validation error for valid {test_case['field']}")

                        ai_insights.append(f"Tested {test_case['test_type']} case for {test_case['field']}")

                    except Exception as e:
                        issues.append(f"Error testing {test_case['field']}: {str(e)}")

            # AI-powered form analysis
            form_analysis = self.ai_intelligence.analyze_page_with_ai(form_html)
            if form_analysis.get('security'):
                ai_insights.extend(form_analysis['security'][:2])

        status = "FAILED" if issues else "PASSED"
        details = f"AI-tested {tested_forms} forms | Insights: {len(ai_insights)} | Issues: {len(issues)}"
        if ai_insights:
            details += f" | AI: {'; '.join(ai_insights[:3])}"

        return TestResult("forms", status, details, "", self.get_timestamp())

    def _detect_form_type(self, form_html: str):
        """Detect the type of form for better AI analysis"""
        form_lower = form_html.lower()

        if 'login' in form_lower or 'sign in' in form_lower:
            return 'login'
        elif 'signup' in form_lower or 'register' in form_lower or 'sign up' in form_lower:
            return 'signup'
        elif 'contact' in form_lower:
            return 'contact'
        elif 'search' in form_lower:
            return 'search'
        elif 'payment' in form_lower or 'checkout' in form_lower:
            return 'payment'
        else:
            return 'generic'
    
    def test_navigation(self, page):
        """Test navigation functionality"""
        issues = []
        
        # Test main navigation links
        nav_links = page.query_selector_all('nav a, header a')
        working_links = 0
        broken_links = 0
        
        for link in nav_links[:5]:  # Test first 5 links to avoid too many requests
            href = link.get_attribute("href")
            if href and not href.startswith("#") and not href.startswith("mailto:"):
                try:
                    # Test if link is clickable
                    if link.is_visible():
                        working_links += 1
                    else:
                        broken_links += 1
                except:
                    broken_links += 1
        
        if broken_links > 0:
            issues.append(f"{broken_links} navigation links not accessible")
        
        status = "FAILED" if issues else "PASSED"
        details = f"Tested {working_links + broken_links} navigation links" + (f" | Issues: {'; '.join(issues)}" if issues else "")
        
        return TestResult("navigation", status, details, "", self.get_timestamp())
    
    def test_responsive_design(self, page):
        """Test responsive design with detailed overflow analysis"""
        issues = []
        overflow_details = []

        # Test different viewport sizes
        viewports = [
            {"width": 375, "height": 667, "name": "Mobile"},
            {"width": 768, "height": 1024, "name": "Tablet"},
            {"width": 1920, "height": 1080, "name": "Desktop"}
        ]

        for viewport in viewports:
            page.set_viewport_size({"width": viewport["width"], "height": viewport["height"]})
            page.wait_for_timeout(1000)

            # Check for horizontal scrollbar
            has_horizontal_scroll = page.evaluate("document.body.scrollWidth > window.innerWidth")
            if has_horizontal_scroll:
                # Get detailed overflow information
                overflow_info = page.evaluate("""
                    () => {
                        const elements = document.querySelectorAll('*');
                        const overflowElements = [];
                        const viewportWidth = window.innerWidth;

                        elements.forEach(el => {
                            const rect = el.getBoundingClientRect();
                            if (rect.right > viewportWidth && rect.width > 0) {
                                overflowElements.push({
                                    tag: el.tagName,
                                    className: el.className,
                                    id: el.id,
                                    width: Math.round(rect.width),
                                    right: Math.round(rect.right),
                                    overflow: Math.round(rect.right - viewportWidth)
                                });
                            }
                        });

                        // Sort by overflow amount (descending)
                        return overflowElements
                            .sort((a, b) => b.overflow - a.overflow)
                            .slice(0, 3); // Top 3 overflow elements
                    }
                """)

                issues.append(f"Horizontal scroll on {viewport['name']} ({viewport['width']}px)")
                overflow_details.extend([
                    f"  {viewport['name']}: {elem['tag']}" +
                    (f".{elem['className']}" if elem['className'] else "") +
                    (f"#{elem['id']}" if elem['id'] else "") +
                    f" overflows by {elem['overflow']}px"
                    for elem in overflow_info
                ])

            # Take screenshot for each viewport
            screenshot_path = self.take_screenshot(page, f"responsive_{viewport['name'].lower()}")

        status = "FAILED" if issues else "PASSED"
        details = f"Tested {len(viewports)} viewport sizes"
        if issues:
            details += f" | Issues: {'; '.join(issues)}"
            if overflow_details:
                details += f" | Overflow details: {'; '.join(overflow_details[:5])}"  # Limit details

        return TestResult("responsive_design", status, details, screenshot_path, self.get_timestamp())
    
    def test_performance(self, page):
        """Test basic performance metrics"""
        issues = []
        
        # Measure load time
        load_metrics = page.evaluate("""
            () => {
                const timing = performance.timing;
                return {
                    loadTime: timing.loadEventEnd - timing.navigationStart,
                    domReady: timing.domContentLoadedEventEnd - timing.navigationStart,
                    firstPaint: performance.getEntriesByType('paint')[0]?.startTime || 0
                };
            }
        """)
        
        if load_metrics["loadTime"] > 5000:
            issues.append(f"Slow load time: {load_metrics['loadTime']}ms")
        
        # Check for large images
        large_images = page.evaluate("""
            () => Array.from(document.images)
                .filter(img => img.naturalWidth > 2000 || img.naturalHeight > 2000)
                .length
        """)
        
        if large_images > 0:
            issues.append(f"{large_images} oversized images detected")
        
        status = "FAILED" if issues else "PASSED"
        details = f"Load: {load_metrics['loadTime']}ms, DOM: {load_metrics['domReady']}ms" + (f" | Issues: {'; '.join(issues)}" if issues else "")
        
        return TestResult("performance", status, details, "", self.get_timestamp())
    
    def test_security_basics(self, page):
        """Test basic security measures"""
        issues = []
        
        # Check for HTTPS
        if not page.url.startswith("https://") and not page.url.startswith("http://localhost"):
            issues.append("Not using HTTPS")
        
        # Check for mixed content
        mixed_content = page.evaluate("""
            () => {
                const resources = performance.getEntriesByType('resource');
                return resources.filter(r => 
                    location.protocol === 'https:' && r.name.startsWith('http:')
                ).length;
            }
        """)
        
        if mixed_content > 0:
            issues.append(f"{mixed_content} mixed content resources")
        
        status = "FAILED" if issues else "PASSED"
        details = "Basic security checks passed" if not issues else "; ".join(issues)
        
        return TestResult("security_basics", status, details, "", self.get_timestamp())
    
    def test_user_flows(self, page):
        """Test critical user flows"""
        issues = []
        flows_tested = 0
        
        # Test signup flow
        signup_button = page.query_selector('text=Sign Up')
        if signup_button and signup_button.is_visible():
            flows_tested += 1
            try:
                signup_button.click()
                page.wait_for_timeout(2000)
                # Check if signup form appears
                email_input = page.query_selector('input[type="email"]')
                if not email_input:
                    issues.append("Signup flow broken - no email input")
            except Exception as e:
                issues.append(f"Signup flow error: {str(e)}")
        
        # Test login flow
        login_button = page.query_selector('text=Log In')
        if login_button and login_button.is_visible():
            flows_tested += 1
            try:
                login_button.click()
                page.wait_for_timeout(2000)
            except Exception as e:
                issues.append(f"Login flow error: {str(e)}")
        
        status = "FAILED" if issues else "PASSED"
        details = f"Tested {flows_tested} user flows" + (f" | Issues: {'; '.join(issues)}" if issues else "")
        
        return TestResult("user_flows", status, details, "", self.get_timestamp())

    def test_ai_comprehensive_analysis(self, page):
        """AI-Powered comprehensive page analysis"""
        print("🧠 Running AI comprehensive analysis...")

        # Get page content for AI analysis
        page_content = page.content()
        screenshot_path = self.take_screenshot(page, "ai_analysis")

        # Run AI analysis
        ai_insights = self.ai_intelligence.analyze_page_with_ai(page_content, screenshot_path)

        # Compile findings
        findings = []
        critical_issues = 0

        for category, issues in ai_insights.items():
            if issues and len(issues) > 0:
                findings.extend([f"{category.title()}: {issue}" for issue in issues[:2]])
                if category in ['security', 'accessibility']:
                    critical_issues += len(issues)

        # Generate recommendations
        recommendations = ai_insights.get('tests', [])

        status = "FAILED" if critical_issues > 2 else "PASSED"
        details = f"AI analyzed {len(ai_insights)} categories | Critical: {critical_issues} | Recommendations: {len(recommendations)}"

        if findings:
            details += f" | Key findings: {'; '.join(findings[:3])}"

        return TestResult("ai_comprehensive_analysis", status, details, screenshot_path, self.get_timestamp())

    def take_screenshot(self, page, name: str = "screenshot"):
        """Take screenshot and return path"""
        timestamp = int(time.time())
        path = os.path.join(self.screenshots_dir, f"{name}_{timestamp}.png")
        page.screenshot(path=path, full_page=True)
        return path
    
    def get_timestamp(self):
        """Get current timestamp"""
        return time.strftime("%Y-%m-%d %H:%M:%S")
    
    def generate_report(self):
        """Generate comprehensive test report"""
        passed = len([r for r in self.results if r.status == "PASSED"])
        failed = len([r for r in self.results if r.status == "FAILED"])
        errors = len([r for r in self.results if r.status == "ERROR"])
        skipped = len([r for r in self.results if r.status == "SKIPPED"])
        
        success_rate = (passed / len(self.results) * 100) if self.results else 0
        
        # Check if AI was used
        ai_status = "🧠 ENABLED" if self.ai_intelligence.ollama_available else "🔧 PATTERN-BASED"

        report = f"""
🤖 AI-Powered Web Testing Report
================================
🧠 AI Intelligence: {ai_status}
📊 Summary:
   Total Tests: {len(self.results)}
   ✅ Passed: {passed}
   ❌ Failed: {failed}
   🚨 Errors: {errors}
   ⏭️ Skipped: {skipped}
   📈 Success Rate: {success_rate:.1f}%

📋 Detailed Results:
"""
        
        for result in self.results:
            status_emoji = {"PASSED": "✅", "FAILED": "❌", "ERROR": "🚨", "SKIPPED": "⏭️"}
            report += f"   {status_emoji.get(result.status, '❓')} {result.test_name}: {result.status}\n"
            if result.details:
                report += f"      Details: {result.details}\n"
            if result.screenshot_path:
                report += f"      Screenshot: {result.screenshot_path}\n"
            report += "\n"
        
        # Save report to file
        report_path = f"test-report-{int(time.time())}.txt"
        with open(report_path, 'w') as f:
            f.write(report)
        
        print(f"📄 Full report saved to: {report_path}")
        return report

    def test_edge_case_authentication(self, page):
        """Test authentication edge cases and boundary conditions"""
        print("🔐 Testing authentication edge cases...")
        issues = []
        edge_cases_tested = 0

        # Navigate to login page
        try:
            page.goto(page.url.replace(page.url.split('/')[-1], 'login'), wait_until="networkidle")

            # Test edge cases for login form
            login_form = page.query_selector('form')
            if login_form:
                email_input = page.query_selector('input[type="email"], input[name*="email"]')
                password_input = page.query_selector('input[type="password"]')

                if email_input and password_input:
                    # Edge case 1: Extremely long email
                    edge_cases_tested += 1
                    long_email = "a" * 300 + "@example.com"
                    email_input.fill(long_email)
                    password_input.fill("password123")
                    page.wait_for_timeout(500)

                    # Check for proper validation
                    error_msg = page.query_selector('.error, .invalid, [role="alert"]')
                    if not error_msg:
                        issues.append("No validation for extremely long email")

                    # Edge case 2: SQL injection attempt
                    edge_cases_tested += 1
                    email_input.fill("'; DROP TABLE users; --")
                    password_input.fill("password")
                    page.wait_for_timeout(500)

                    # Edge case 3: XSS attempt in email
                    edge_cases_tested += 1
                    email_input.fill("<script>alert('xss')</script>@test.com")
                    password_input.fill("password")
                    page.wait_for_timeout(500)

                    # Edge case 4: Unicode characters
                    edge_cases_tested += 1
                    email_input.fill("тест@пример.рф")
                    password_input.fill("пароль123")
                    page.wait_for_timeout(500)

                    # Edge case 5: Empty password with valid email
                    edge_cases_tested += 1
                    email_input.fill("<EMAIL>")
                    password_input.fill("")
                    page.wait_for_timeout(500)

        except Exception as e:
            issues.append(f"Authentication testing error: {str(e)}")

        status = "FAILED" if issues else "PASSED"
        details = f"Tested {edge_cases_tested} auth edge cases | Issues: {len(issues)}"
        if issues:
            details += f" | {'; '.join(issues[:3])}"

        return TestResult("edge_case_authentication", status, details, "", self.get_timestamp())

    def test_edge_case_assessment_system(self, page):
        """Test assessment system edge cases"""
        print("📊 Testing assessment system edge cases...")
        issues = []
        edge_cases_tested = 0

        try:
            # Navigate to assessment page
            page.goto(page.url.replace(page.url.split('/')[-1], 'assessment'), wait_until="networkidle")

            # Look for assessment forms or questions
            questions = page.query_selector_all('input[type="radio"], input[type="checkbox"], select')

            if questions:
                # Edge case 1: Submit without answering any questions
                edge_cases_tested += 1
                submit_btn = page.query_selector('button[type="submit"], input[type="submit"]')
                if submit_btn:
                    submit_btn.click()
                    page.wait_for_timeout(1000)

                    # Check for proper validation
                    error_msg = page.query_selector('.error, .invalid, [role="alert"]')
                    if not error_msg:
                        issues.append("No validation for empty assessment submission")

                # Edge case 2: Rapid clicking/double submission
                edge_cases_tested += 1
                if submit_btn:
                    submit_btn.click()
                    submit_btn.click()  # Double click
                    page.wait_for_timeout(500)

                # Edge case 3: Answer all questions with same value
                edge_cases_tested += 1
                for question in questions[:5]:  # Test first 5 questions
                    if question.get_attribute('type') == 'radio':
                        question.check()
                    elif question.get_attribute('type') == 'checkbox':
                        question.check()

                # Edge case 4: Change answers rapidly
                edge_cases_tested += 1
                for question in questions[:3]:
                    if question.get_attribute('type') == 'radio':
                        question.check()
                        page.wait_for_timeout(100)
                        question.uncheck() if hasattr(question, 'uncheck') else None
                        page.wait_for_timeout(100)
                        question.check()

        except Exception as e:
            issues.append(f"Assessment testing error: {str(e)}")

        status = "FAILED" if issues else "PASSED"
        details = f"Tested {edge_cases_tested} assessment edge cases | Issues: {len(issues)}"
        if issues:
            details += f" | {'; '.join(issues[:2])}"

        return TestResult("edge_case_assessment_system", status, details, "", self.get_timestamp())

    def test_edge_case_data_validation(self, page):
        """Test data validation edge cases across all forms"""
        print("🔍 Testing data validation edge cases...")
        issues = []
        edge_cases_tested = 0

        # Find all input fields on the page
        inputs = page.query_selector_all('input, textarea, select')

        for input_field in inputs[:10]:  # Test first 10 inputs to avoid timeout
            input_type = input_field.get_attribute('type') or 'text'

            try:
                # Edge case 1: Maximum length boundary
                edge_cases_tested += 1
                max_length = input_field.get_attribute('maxlength')
                if max_length:
                    max_val = int(max_length)
                    # Test exactly at boundary
                    input_field.fill("a" * max_val)
                    page.wait_for_timeout(200)
                    # Test one character over boundary
                    input_field.fill("a" * (max_val + 1))
                    page.wait_for_timeout(200)

                # Edge case 2: Special characters
                edge_cases_tested += 1
                special_chars = "!@#$%^&*()_+-=[]{}|;':\",./<>?"
                input_field.fill(special_chars)
                page.wait_for_timeout(200)

                # Edge case 3: Unicode and emoji
                edge_cases_tested += 1
                unicode_text = "🚀 тест 中文 العربية"
                input_field.fill(unicode_text)
                page.wait_for_timeout(200)

                # Edge case 4: Very long input
                edge_cases_tested += 1
                very_long_input = "x" * 10000
                input_field.fill(very_long_input)
                page.wait_for_timeout(200)

            except Exception as e:
                issues.append(f"Validation error on {input_type} field: {str(e)}")

        status = "FAILED" if issues else "PASSED"
        details = f"Tested {edge_cases_tested} validation edge cases | Issues: {len(issues)}"
        if issues:
            details += f" | {'; '.join(issues[:2])}"

        return TestResult("edge_case_data_validation", status, details, "", self.get_timestamp())

    def test_edge_case_error_handling(self, page):
        """Test error handling edge cases"""
        print("⚠️ Testing error handling edge cases...")
        issues = []
        edge_cases_tested = 0

        try:
            # Edge case 1: Navigate to non-existent page
            edge_cases_tested += 1
            original_url = page.url
            page.goto(original_url + "/non-existent-page-12345", wait_until="networkidle")

            # Check if proper 404 page is shown
            page_content = page.content().lower()
            if "404" not in page_content and "not found" not in page_content:
                issues.append("No proper 404 error page")

            # Return to original page
            page.goto(original_url, wait_until="networkidle")

            # Edge case 2: Test with malformed URLs
            edge_cases_tested += 1
            malformed_urls = [
                original_url + "/../../../etc/passwd",
                original_url + "/%2e%2e%2f%2e%2e%2f",
                original_url + "/..%2f..%2f",
            ]

            for malformed_url in malformed_urls:
                try:
                    page.goto(malformed_url, wait_until="networkidle")
                    # Should not expose sensitive information
                    content = page.content().lower()
                    if "root:" in content or "password" in content:
                        issues.append("Potential path traversal vulnerability")
                    page.goto(original_url, wait_until="networkidle")
                except:
                    pass  # Expected to fail

            # Edge case 3: Test JavaScript errors
            edge_cases_tested += 1
            page.evaluate("throw new Error('Test error')")
            page.wait_for_timeout(1000)

        except Exception as e:
            issues.append(f"Error handling test failed: {str(e)}")

        status = "FAILED" if issues else "PASSED"
        details = f"Tested {edge_cases_tested} error handling cases | Issues: {len(issues)}"
        if issues:
            details += f" | {'; '.join(issues[:2])}"

        return TestResult("edge_case_error_handling", status, details, "", self.get_timestamp())

    def test_edge_case_boundary_conditions(self, page):
        """Test boundary conditions and limits"""
        print("📏 Testing boundary conditions...")
        issues = []
        edge_cases_tested = 0

        try:
            # Edge case 1: Test with extremely large viewport
            edge_cases_tested += 1
            page.set_viewport_size({"width": 8000, "height": 6000})
            page.wait_for_timeout(1000)

            # Check for layout issues
            has_horizontal_scroll = page.evaluate("document.body.scrollWidth > window.innerWidth")
            if has_horizontal_scroll:
                issues.append("Layout breaks at very large viewport")

            # Edge case 2: Test with extremely small viewport
            edge_cases_tested += 1
            page.set_viewport_size({"width": 200, "height": 200})
            page.wait_for_timeout(1000)

            # Check if content is still accessible
            visible_elements = page.query_selector_all(':visible')
            if len(visible_elements) < 5:
                issues.append("Too few elements visible at very small viewport")

            # Reset viewport
            page.set_viewport_size({"width": 1920, "height": 1080})

            # Edge case 3: Test rapid navigation
            edge_cases_tested += 1
            current_url = page.url
            for i in range(5):
                page.goto(current_url + "#test" + str(i))
                page.wait_for_timeout(100)

        except Exception as e:
            issues.append(f"Boundary testing error: {str(e)}")

        status = "FAILED" if issues else "PASSED"
        details = f"Tested {edge_cases_tested} boundary conditions | Issues: {len(issues)}"
        if issues:
            details += f" | {'; '.join(issues[:2])}"

        return TestResult("edge_case_boundary_conditions", status, details, "", self.get_timestamp())

    def test_edge_case_concurrent_operations(self, page):
        """Test concurrent operations and race conditions"""
        print("🏃‍♂️ Testing concurrent operations...")
        issues = []
        edge_cases_tested = 0

        try:
            # Edge case 1: Multiple rapid form submissions
            edge_cases_tested += 1
            forms = page.query_selector_all('form')
            if forms:
                form = forms[0]
                submit_btn = form.query_selector('button[type="submit"], input[type="submit"]')
                if submit_btn:
                    # Rapid multiple clicks
                    for i in range(5):
                        submit_btn.click()
                        page.wait_for_timeout(50)

            # Edge case 2: Rapid navigation while loading
            edge_cases_tested += 1
            current_url = page.url
            page.goto(current_url + "?test=1")
            page.goto(current_url + "?test=2")
            page.goto(current_url + "?test=3")
            page.wait_for_timeout(1000)

            # Edge case 3: Multiple AJAX requests simulation
            edge_cases_tested += 1
            page.evaluate("""
                for(let i = 0; i < 10; i++) {
                    fetch('/api/test', {method: 'GET'}).catch(() => {});
                }
            """)
            page.wait_for_timeout(2000)

        except Exception as e:
            issues.append(f"Concurrent operations test failed: {str(e)}")

        status = "FAILED" if issues else "PASSED"
        details = f"Tested {edge_cases_tested} concurrent operations | Issues: {len(issues)}"
        if issues:
            details += f" | {'; '.join(issues[:2])}"

        return TestResult("edge_case_concurrent_operations", status, details, "", self.get_timestamp())

    def test_edge_case_malformed_inputs(self, page):
        """Test malformed and malicious inputs"""
        print("🦠 Testing malformed inputs...")
        issues = []
        edge_cases_tested = 0

        malicious_payloads = [
            "<script>alert('XSS')</script>",
            "'; DROP TABLE users; --",
            "../../../etc/passwd",
            "%3Cscript%3Ealert('XSS')%3C/script%3E",
            "javascript:alert('XSS')",
            "<img src=x onerror=alert('XSS')>",
            "{{7*7}}",  # Template injection
            "${7*7}",   # Expression injection
            "\x00\x01\x02\x03",  # Null bytes
        ]

        inputs = page.query_selector_all('input, textarea')

        for input_field in inputs[:5]:  # Test first 5 inputs
            for payload in malicious_payloads[:5]:  # Test first 5 payloads
                try:
                    edge_cases_tested += 1
                    input_field.fill(payload)
                    page.wait_for_timeout(200)

                    # Check if payload is reflected in page
                    page_content = page.content()
                    if payload in page_content and "<script>" in payload:
                        issues.append("Potential XSS vulnerability detected")

                except Exception as e:
                    # Expected for some malicious inputs
                    pass

        status = "FAILED" if issues else "PASSED"
        details = f"Tested {edge_cases_tested} malformed inputs | Issues: {len(issues)}"
        if issues:
            details += f" | {'; '.join(issues[:2])}"

        return TestResult("edge_case_malformed_inputs", status, details, "", self.get_timestamp())

    def test_edge_case_session_management(self, page):
        """Test session management edge cases"""
        print("🔑 Testing session management...")
        issues = []
        edge_cases_tested = 0

        try:
            # Edge case 1: Test session with expired cookies
            edge_cases_tested += 1
            # Set expired cookie
            page.context.add_cookies([{
                'name': 'session',
                'value': 'expired_session_token',
                'domain': 'localhost',
                'path': '/',
                'expires': 0  # Expired
            }])
            page.reload()
            page.wait_for_timeout(1000)

            # Edge case 2: Test with malformed session data
            edge_cases_tested += 1
            page.context.add_cookies([{
                'name': 'session',
                'value': 'malformed_session_data_12345',
                'domain': 'localhost',
                'path': '/'
            }])
            page.reload()
            page.wait_for_timeout(1000)

            # Edge case 3: Test session fixation
            edge_cases_tested += 1
            original_cookies = page.context.cookies()
            page.goto(page.url + "?sessionid=fixed_session_123")
            page.wait_for_timeout(1000)
            new_cookies = page.context.cookies()

            # Check if session ID changed (good security practice)
            session_changed = len(original_cookies) != len(new_cookies)
            if not session_changed:
                issues.append("Session ID may not be regenerated properly")

        except Exception as e:
            issues.append(f"Session management test failed: {str(e)}")

        status = "FAILED" if issues else "PASSED"
        details = f"Tested {edge_cases_tested} session management cases | Issues: {len(issues)}"
        if issues:
            details += f" | {'; '.join(issues[:2])}"

        return TestResult("edge_case_session_management", status, details, "", self.get_timestamp())

    def cleanup(self):
        """Clean up resources"""
        self.browser.close()
        self.playwright.stop()

# CLI Usage
if __name__ == "__main__":
    import sys
    
    if len(sys.argv) < 2:
        print("Usage: python ai_web_tester.py <url> [task_description]")
        sys.exit(1)
    
    url = sys.argv[1]
    task = sys.argv[2] if len(sys.argv) > 2 else "comprehensive testing"
    
    tester = AIWebTester(headless=False)
    try:
        report = tester.run_comprehensive_test(url, task)
        print(report)
    finally:
        tester.cleanup()
