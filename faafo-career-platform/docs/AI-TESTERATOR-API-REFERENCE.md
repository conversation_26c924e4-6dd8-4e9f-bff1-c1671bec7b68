# 🔧 AI TESTERATOR - API Reference

> **Complete API documentation for the AI Testerator testing framework**

## 📚 **Core Classes**

### **AIWebTester**

The main testing class that orchestrates all testing operations.

```python
class AIWebTester:
    def __init__(self, headless: bool = False, timeout: int = 30000)
```

#### **Parameters**
- `headless` (bool): Run browser in headless mode (default: False)
- `timeout` (int): Default timeout for operations in milliseconds (default: 30000)

#### **Methods**

##### **run_comprehensive_test(url: str, task: str = "") → None**
Executes the complete test suite on the specified URL.

```python
tester = AIWebTester()
tester.run_comprehensive_test("https://example.com", "Test user registration")
```

##### **test_page_load(page) → TestResult**
Tests basic page loading functionality.

```python
result = tester.test_page_load(page)
# Returns: TestResult with page load metrics
```

##### **test_accessibility(page) → TestResult**
Performs comprehensive accessibility testing.

```python
result = tester.test_accessibility(page)
# Checks: Alt text, form labels, ARIA attributes, heading structure
```

##### **test_forms(page) → TestResult**
AI-enhanced form functionality testing.

```python
result = tester.test_forms(page)
# Features: AI-generated test cases, XSS testing, validation checks
```

##### **test_navigation(page) → TestResult**
Tests navigation links and menu functionality.

```python
result = tester.test_navigation(page)
# Validates: Link functionality, menu accessibility, mobile navigation
```

##### **test_responsive_design(page) → TestResult**
Multi-viewport responsive design testing.

```python
result = tester.test_responsive_design(page)
# Tests: Mobile (375x667), Tablet (768x1024), Desktop (1920x1080)
```

##### **test_performance(page) → TestResult**
Performance and load time analysis.

```python
result = tester.test_performance(page)
# Measures: Load time, DOM ready time, resource optimization
```

##### **test_security_basics(page) → TestResult**
Basic security vulnerability scanning.

```python
result = tester.test_security_basics(page)
# Checks: HTTPS, mixed content, basic XSS protection
```

##### **test_user_flows(page) → TestResult**
Tests critical user journeys.

```python
result = tester.test_user_flows(page)
# Tests: Signup flow, login process, key user paths
```

##### **test_ai_comprehensive_analysis(page) → TestResult**
AI-powered comprehensive page analysis.

```python
result = tester.test_ai_comprehensive_analysis(page)
# Features: Intelligent issue detection, security analysis, UX recommendations
```

---

### **AITestIntelligence**

The AI engine that powers intelligent test case generation and analysis.

```python
class AITestIntelligence:
    def __init__(self)
```

#### **Methods**

##### **check_ollama() → bool**
Checks if Ollama AI service is available.

```python
ai = AITestIntelligence()
is_available = ai.check_ollama()
# Returns: True if Ollama is running, False otherwise
```

##### **analyze_page_with_ai(page_content: str, screenshot_path: str = None) → dict**
Performs AI-powered page analysis.

```python
analysis = ai.analyze_page_with_ai(html_content, "screenshot.png")
# Returns: {
#   'usability': [...],
#   'security': [...],
#   'accessibility': [...],
#   'performance': [...],
#   'missing': [...],
#   'tests': [...]
# }
```

##### **generate_smart_test_cases(form_html: str, form_type: str = "unknown") → list**
Generates intelligent test cases for forms.

```python
test_cases = ai.generate_smart_test_cases(form_html, "login")
# Returns: [
#   {
#     'field': 'email',
#     'value': '<EMAIL>',
#     'expected_result': 'valid',
#     'test_type': 'happy_path'
#   },
#   ...
# ]
```

---

### **TestResult**

Data class representing the result of a single test.

```python
@dataclass
class TestResult:
    test_name: str
    status: str          # "PASSED", "FAILED", "SKIPPED", "ERROR"
    details: str
    screenshot_path: str
    timestamp: str
```

#### **Usage Example**

```python
result = TestResult(
    test_name="page_load",
    status="PASSED",
    details="Page loaded in 250ms",
    screenshot_path="screenshots/page_load_123456.png",
    timestamp="2024-01-15 10:30:45"
)
```

---

## 🎯 **Advanced Usage Patterns**

### **Custom Test Implementation**

```python
from ai_web_tester import AIWebTester, TestResult
from playwright.sync_api import sync_playwright

class CustomEcommerceTest(AIWebTester):
    def test_product_search(self, page):
        """Custom test for product search functionality"""
        issues = []
        
        # Navigate to search
        search_box = page.query_selector('[data-testid="search-input"]')
        if not search_box:
            issues.append("Search box not found")
        
        # Test search functionality
        search_box.fill("laptop")
        page.keyboard.press("Enter")
        page.wait_for_timeout(2000)
        
        # Verify results
        results = page.query_selector_all('.product-item')
        if len(results) == 0:
            issues.append("No search results displayed")
        
        status = "FAILED" if issues else "PASSED"
        details = f"Found {len(results)} products" + (f" | Issues: {'; '.join(issues)}" if issues else "")
        
        return TestResult("product_search", status, details, "", self.get_timestamp())
    
    def test_shopping_cart(self, page):
        """Custom test for shopping cart operations"""
        # Add product to cart
        add_to_cart_btn = page.query_selector('[data-testid="add-to-cart"]')
        if add_to_cart_btn:
            add_to_cart_btn.click()
            page.wait_for_timeout(1000)
        
        # Verify cart update
        cart_count = page.query_selector('.cart-count')
        cart_items = int(cart_count.inner_text()) if cart_count else 0
        
        status = "PASSED" if cart_items > 0 else "FAILED"
        details = f"Cart contains {cart_items} items"
        
        return TestResult("shopping_cart", status, details, "", self.get_timestamp())

# Usage
custom_tester = CustomEcommerceTest()
custom_tester.run_comprehensive_test("https://mystore.com")
```

### **Batch Testing Multiple URLs**

```python
def batch_test_urls(urls: list, task: str = "Batch testing"):
    """Test multiple URLs with the same configuration"""
    results = {}
    
    for url in urls:
        print(f"🧪 Testing {url}...")
        tester = AIWebTester(headless=True)
        
        try:
            tester.run_comprehensive_test(url, task)
            results[url] = "SUCCESS"
        except Exception as e:
            results[url] = f"ERROR: {str(e)}"
        finally:
            tester.cleanup()
    
    return results

# Usage
urls = [
    "https://staging.myapp.com",
    "https://production.myapp.com",
    "https://beta.myapp.com"
]

results = batch_test_urls(urls, "Multi-environment testing")
```

### **Custom AI Analysis**

```python
def custom_ai_analysis(page_content: str):
    """Implement custom AI analysis logic"""
    ai = AITestIntelligence()
    
    # Custom prompts for specific analysis
    custom_analysis = {
        'ecommerce_specific': [],
        'security_deep_dive': [],
        'performance_insights': []
    }
    
    # E-commerce specific checks
    if 'cart' in page_content.lower() or 'checkout' in page_content.lower():
        custom_analysis['ecommerce_specific'].append("E-commerce functionality detected")
        
        # Check for SSL indicators
        if 'https' not in page_content:
            custom_analysis['security_deep_dive'].append("Missing HTTPS for e-commerce")
    
    # Performance insights
    if 'script' in page_content:
        script_count = page_content.count('<script')
        if script_count > 10:
            custom_analysis['performance_insights'].append(f"High script count: {script_count}")
    
    return custom_analysis
```

---

## 🔧 **Configuration Options**

### **Environment Variables**

```python
import os

# AI Configuration
OLLAMA_HOST = os.getenv('OLLAMA_HOST', 'http://localhost:11434')
AI_MODEL = os.getenv('AI_MODEL', 'llama3.2:1b')
AI_TIMEOUT = int(os.getenv('AI_TIMEOUT', '30'))

# Browser Configuration
BROWSER_TYPE = os.getenv('BROWSER_TYPE', 'chromium')  # chromium, firefox, webkit
HEADLESS = os.getenv('HEADLESS', 'false').lower() == 'true'
VIEWPORT_WIDTH = int(os.getenv('VIEWPORT_WIDTH', '1920'))
VIEWPORT_HEIGHT = int(os.getenv('VIEWPORT_HEIGHT', '1080'))

# Output Configuration
SCREENSHOT_DIR = os.getenv('SCREENSHOT_DIR', 'test-screenshots')
REPORT_FORMAT = os.getenv('REPORT_FORMAT', 'txt')  # txt, json, html
OUTPUT_DIR = os.getenv('OUTPUT_DIR', '.')
```

### **Custom Configuration Class**

```python
class TestConfig:
    def __init__(self):
        self.headless = False
        self.timeout = 30000
        self.viewport = {"width": 1920, "height": 1080}
        self.screenshot_quality = 80
        self.ai_enabled = True
        self.ai_model = "llama3.2:1b"
        self.browser_type = "chromium"
        self.output_format = "txt"
        
    @classmethod
    def from_env(cls):
        """Create configuration from environment variables"""
        config = cls()
        config.headless = os.getenv('HEADLESS', 'false').lower() == 'true'
        config.timeout = int(os.getenv('TIMEOUT', '30000'))
        config.ai_model = os.getenv('AI_MODEL', 'llama3.2:1b')
        return config

# Usage
config = TestConfig.from_env()
tester = AIWebTester(headless=config.headless, timeout=config.timeout)
```

---

## 📊 **Report Generation**

### **Custom Report Formats**

```python
def generate_json_report(results: list) -> str:
    """Generate JSON format report"""
    import json
    
    report_data = {
        "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
        "total_tests": len(results),
        "passed": len([r for r in results if r.status == "PASSED"]),
        "failed": len([r for r in results if r.status == "FAILED"]),
        "skipped": len([r for r in results if r.status == "SKIPPED"]),
        "tests": [
            {
                "name": r.test_name,
                "status": r.status,
                "details": r.details,
                "screenshot": r.screenshot_path,
                "timestamp": r.timestamp
            }
            for r in results
        ]
    }
    
    return json.dumps(report_data, indent=2)

def generate_html_report(results: list) -> str:
    """Generate HTML format report"""
    html = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>AI Testerator Report</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .passed { color: green; }
            .failed { color: red; }
            .skipped { color: orange; }
            .test-result { margin: 10px 0; padding: 10px; border: 1px solid #ddd; }
        </style>
    </head>
    <body>
        <h1>🤖 AI Testerator Report</h1>
    """
    
    for result in results:
        status_class = result.status.lower()
        html += f"""
        <div class="test-result">
            <h3 class="{status_class}">{result.test_name}: {result.status}</h3>
            <p>{result.details}</p>
            <small>Timestamp: {result.timestamp}</small>
        </div>
        """
    
    html += "</body></html>"
    return html
```

---

## 🚀 **Integration Examples**

### **GitHub Actions Integration**

```yaml
name: AI Testerator CI
on: [push, pull_request]

jobs:
  ai-testing:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.9'
      
      - name: Install dependencies
        run: |
          pip install playwright requests
          playwright install chromium
      
      - name: Run AI Testerator
        run: |
          python3 ai_web_tester.py ${{ secrets.STAGING_URL }} "CI/CD Testing"
        env:
          HEADLESS: true
          AI_MODEL: llama3.2:1b
      
      - name: Upload Reports
        uses: actions/upload-artifact@v3
        with:
          name: test-reports
          path: |
            test-report-*.txt
            test-screenshots/
```

### **Jenkins Integration**

```groovy
pipeline {
    agent any
    
    environment {
        HEADLESS = 'true'
        AI_MODEL = 'llama3.2:1b'
    }
    
    stages {
        stage('Setup') {
            steps {
                sh 'pip install playwright requests'
                sh 'playwright install chromium'
            }
        }
        
        stage('AI Testing') {
            steps {
                sh 'python3 ai_web_tester.py ${STAGING_URL} "Jenkins CI Testing"'
            }
        }
        
        stage('Archive Results') {
            steps {
                archiveArtifacts artifacts: 'test-report-*.txt, test-screenshots/**'
                publishHTML([
                    allowMissing: false,
                    alwaysLinkToLastBuild: true,
                    keepAll: true,
                    reportDir: '.',
                    reportFiles: 'test-report-*.txt',
                    reportName: 'AI Testerator Report'
                ])
            }
        }
    }
}
```

---

## 🔍 **Error Handling**

### **Exception Types**

```python
class AITesteratorError(Exception):
    """Base exception for AI Testerator"""
    pass

class BrowserLaunchError(AITesteratorError):
    """Raised when browser fails to launch"""
    pass

class PageLoadError(AITesteratorError):
    """Raised when page fails to load"""
    pass

class AIServiceError(AITesteratorError):
    """Raised when AI service is unavailable"""
    pass

# Usage with error handling
try:
    tester = AIWebTester()
    tester.run_comprehensive_test("https://example.com")
except BrowserLaunchError as e:
    print(f"Browser failed to launch: {e}")
except PageLoadError as e:
    print(f"Page failed to load: {e}")
except AIServiceError as e:
    print(f"AI service unavailable: {e}")
except Exception as e:
    print(f"Unexpected error: {e}")
```

---

## 📈 **Performance Optimization**

### **Memory Management**

```python
def optimized_batch_testing(urls: list):
    """Memory-efficient batch testing"""
    for url in urls:
        # Create fresh instance for each test
        tester = AIWebTester(headless=True)
        
        try:
            tester.run_comprehensive_test(url)
        finally:
            # Ensure cleanup
            tester.cleanup()
            del tester  # Explicit cleanup
```

### **Parallel Testing**

```python
import concurrent.futures
import threading

def parallel_test_execution(urls: list, max_workers: int = 3):
    """Run tests in parallel for faster execution"""
    results = {}
    
    def test_single_url(url):
        tester = AIWebTester(headless=True)
        try:
            tester.run_comprehensive_test(url)
            return url, "SUCCESS"
        except Exception as e:
            return url, f"ERROR: {str(e)}"
        finally:
            tester.cleanup()
    
    with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
        future_to_url = {executor.submit(test_single_url, url): url for url in urls}
        
        for future in concurrent.futures.as_completed(future_to_url):
            url, result = future.result()
            results[url] = result
    
    return results
```

---

**🎯 This API reference provides everything you need to extend and customize AI Testerator for your specific testing needs!**
