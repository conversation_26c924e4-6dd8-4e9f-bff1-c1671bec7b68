#!/usr/bin/env python3
"""
Critical Edge Case Testing for FAAFO Career Platform
Focuses on the most important security and functionality edge cases
"""

import subprocess
import time
import os
import sys
from datetime import datetime

def run_critical_edge_case_tests():
    """Run critical edge case tests focusing on security and core functionality"""
    
    print("🔥 FAAFO Career Platform - Critical Edge Case Testing")
    print("=" * 60)
    print("🎯 Focus: Security vulnerabilities, data integrity, error handling")
    print()
    
    base_url = "http://localhost:3000"
    
    # Critical test scenarios
    critical_tests = [
        {
            'name': 'Authentication Security',
            'url': f'{base_url}/login',
            'description': 'SQL injection, XSS, brute force protection',
            'priority': 'CRITICAL'
        },
        {
            'name': 'Assessment Data Integrity',
            'url': f'{base_url}/assessment',
            'description': 'Form validation, concurrent submissions, data corruption',
            'priority': 'HIGH'
        },
        {
            'name': 'User Input Validation',
            'url': f'{base_url}/signup',
            'description': 'Malformed inputs, boundary conditions, injection attacks',
            'priority': 'CRITICAL'
        },
        {
            'name': 'Session Management',
            'url': f'{base_url}/dashboard',
            'description': 'Session fixation, expired tokens, unauthorized access',
            'priority': 'CRITICAL'
        },
        {
            'name': 'API Endpoint Security',
            'url': f'{base_url}/api/assessment',
            'description': 'Direct API access, parameter tampering, rate limiting',
            'priority': 'HIGH'
        }
    ]
    
    results = []
    
    for i, test in enumerate(critical_tests, 1):
        print(f"\n🧪 Test {i}/{len(critical_tests)}: {test['name']}")
        print(f"🎯 Priority: {test['priority']}")
        print(f"📍 URL: {test['url']}")
        print(f"📋 Focus: {test['description']}")
        print("-" * 50)
        
        # Run the test
        cmd = ["python3", "ai_web_tester.py", test['url'], f"Critical edge case testing: {test['name']}"]
        
        try:
            start_time = time.time()
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=180)
            end_time = time.time()
            
            success = result.returncode == 0
            duration = end_time - start_time
            
            results.append({
                'name': test['name'],
                'priority': test['priority'],
                'success': success,
                'duration': duration,
                'output': result.stdout if success else result.stderr
            })
            
            if success:
                print(f"✅ PASSED ({duration:.1f}s)")
            else:
                print(f"❌ FAILED ({duration:.1f}s)")
                print(f"Error: {result.stderr[:200]}...")
                
        except subprocess.TimeoutExpired:
            print("⏰ TIMEOUT (3 minutes)")
            results.append({
                'name': test['name'],
                'priority': test['priority'],
                'success': False,
                'duration': 180,
                'output': "Test timed out after 3 minutes"
            })
        except Exception as e:
            print(f"💥 CRASHED: {str(e)}")
            results.append({
                'name': test['name'],
                'priority': test['priority'],
                'success': False,
                'duration': 0,
                'output': f"Test crashed: {str(e)}"
            })
        
        # Brief pause between tests
        time.sleep(1)
    
    # Generate critical findings report
    generate_critical_report(results)

def generate_critical_report(results):
    """Generate a focused report on critical findings"""
    
    total_tests = len(results)
    passed_tests = sum(1 for r in results if r['success'])
    failed_tests = total_tests - passed_tests
    critical_failures = sum(1 for r in results if not r['success'] and r['priority'] == 'CRITICAL')
    
    print(f"\n🎯 CRITICAL EDGE CASE TESTING RESULTS")
    print("=" * 60)
    print(f"📊 Summary:")
    print(f"   Total Tests: {total_tests}")
    print(f"   ✅ Passed: {passed_tests}")
    print(f"   ❌ Failed: {failed_tests}")
    print(f"   🔴 Critical Failures: {critical_failures}")
    print(f"   📈 Success Rate: {(passed_tests/total_tests*100):.1f}%")
    
    # Critical security findings
    print(f"\n🚨 SECURITY ANALYSIS:")
    security_issues = []
    
    for result in results:
        if not result['success']:
            output_lower = result['output'].lower()
            if any(keyword in output_lower for keyword in ['injection', 'xss', 'sql', 'script']):
                security_issues.append(f"🔴 {result['name']}: Potential security vulnerability")
            elif 'timeout' in output_lower:
                security_issues.append(f"🟡 {result['name']}: Performance/DoS vulnerability")
            elif 'error' in output_lower:
                security_issues.append(f"🟡 {result['name']}: Error handling issue")
    
    if security_issues:
        for issue in security_issues:
            print(f"   {issue}")
    else:
        print("   🎉 No critical security vulnerabilities detected!")
    
    # Detailed results
    print(f"\n📋 DETAILED RESULTS:")
    for result in results:
        status = "✅ PASSED" if result['success'] else "❌ FAILED"
        priority_emoji = "🔴" if result['priority'] == 'CRITICAL' else "🟡"
        print(f"   {priority_emoji} {result['name']}: {status} ({result['duration']:.1f}s)")
        if not result['success']:
            print(f"      ⚠️ {result['output'][:100]}...")
    
    # Recommendations
    print(f"\n💡 IMMEDIATE ACTIONS REQUIRED:")
    if critical_failures > 0:
        print(f"   🚨 {critical_failures} CRITICAL security tests failed!")
        print("   1. 🔒 Review authentication and input validation immediately")
        print("   2. 🛡️ Implement additional security measures")
        print("   3. 🧪 Fix all critical issues before production deployment")
    else:
        print("   ✅ All critical security tests passed")
        print("   1. 🔍 Review any high-priority failures")
        print("   2. 📊 Monitor these scenarios in production")
    
    print("   3. 🤖 Integrate these tests into CI/CD pipeline")
    print("   4. 📈 Set up automated security scanning")
    
    # Save report
    timestamp = int(time.time())
    report_file = f"critical-edge-case-report-{timestamp}.txt"
    
    with open(report_file, 'w') as f:
        f.write(f"FAAFO Career Platform - Critical Edge Case Testing Report\n")
        f.write(f"Generated: {datetime.now()}\n")
        f.write(f"Total Tests: {total_tests}, Passed: {passed_tests}, Failed: {failed_tests}\n")
        f.write(f"Critical Failures: {critical_failures}\n\n")
        
        for result in results:
            f.write(f"{result['name']}: {'PASSED' if result['success'] else 'FAILED'}\n")
            f.write(f"Priority: {result['priority']}, Duration: {result['duration']:.1f}s\n")
            if not result['success']:
                f.write(f"Output: {result['output']}\n")
            f.write("\n")
    
    print(f"\n📄 Report saved to: {report_file}")
    
    # Return exit code based on critical failures
    return 0 if critical_failures == 0 else 1

def main():
    """Main execution"""
    # Check if server is running
    import requests
    try:
        response = requests.get("http://localhost:3000", timeout=5)
        print("✅ Development server detected")
    except:
        print("❌ Development server not running! Start with: npm run dev")
        sys.exit(1)
    
    # Run critical tests
    exit_code = run_critical_edge_case_tests()
    sys.exit(exit_code)

if __name__ == "__main__":
    main()
