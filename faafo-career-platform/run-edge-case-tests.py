#!/usr/bin/env python3
"""
Comprehensive Edge Case Testing Suite for FAAFO Career Platform
This script runs extensive edge case testing using the enhanced AI testerator
"""

import subprocess
import time
import os
import sys
from datetime import datetime

class EdgeCaseTestRunner:
    def __init__(self):
        self.base_url = "http://localhost:3000"
        self.test_results = []
        self.start_time = datetime.now()
        
    def run_test_suite(self, page_path, test_description, specific_tests=None):
        """Run edge case tests for a specific page"""
        url = f"{self.base_url}{page_path}"
        print(f"\n🎯 Testing: {test_description}")
        print(f"📍 URL: {url}")
        print("=" * 80)
        
        # Prepare command
        cmd = ["python3", "ai_web_tester.py", url, f"Edge case testing: {test_description}"]
        
        try:
            # Run the test
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
            
            # Parse results
            success = result.returncode == 0
            output = result.stdout if success else result.stderr
            
            self.test_results.append({
                'page': page_path,
                'description': test_description,
                'success': success,
                'output': output,
                'timestamp': datetime.now()
            })
            
            if success:
                print("✅ Test completed successfully")
            else:
                print("❌ Test failed")
                print(f"Error: {result.stderr}")
                
        except subprocess.TimeoutExpired:
            print("⏰ Test timed out after 5 minutes")
            self.test_results.append({
                'page': page_path,
                'description': test_description,
                'success': False,
                'output': "Test timed out",
                'timestamp': datetime.now()
            })
        except Exception as e:
            print(f"💥 Test crashed: {str(e)}")
            self.test_results.append({
                'page': page_path,
                'description': test_description,
                'success': False,
                'output': f"Test crashed: {str(e)}",
                'timestamp': datetime.now()
            })
    
    def run_comprehensive_edge_case_testing(self):
        """Run comprehensive edge case testing across all major application areas"""
        
        print("🚀 FAAFO Career Platform - Comprehensive Edge Case Testing")
        print("=" * 80)
        print(f"🕐 Started at: {self.start_time}")
        print(f"🌐 Base URL: {self.base_url}")
        print()
        
        # Test suite configuration
        test_suites = [
            {
                'path': '/',
                'description': 'Homepage - Landing page edge cases',
                'focus': 'Navigation, responsive design, performance under load'
            },
            {
                'path': '/login',
                'description': 'Authentication - Login edge cases',
                'focus': 'SQL injection, XSS, brute force protection, session management'
            },
            {
                'path': '/signup',
                'description': 'Registration - Signup edge cases',
                'focus': 'Input validation, duplicate emails, malformed data'
            },
            {
                'path': '/assessment',
                'description': 'Assessment System - Career assessment edge cases',
                'focus': 'Form validation, concurrent submissions, data integrity'
            },
            {
                'path': '/dashboard',
                'description': 'Dashboard - User dashboard edge cases',
                'focus': 'Data loading, error states, unauthorized access'
            },
            {
                'path': '/resources',
                'description': 'Learning Resources - Resource browsing edge cases',
                'focus': 'Search functionality, filtering, pagination limits'
            },
            {
                'path': '/forum',
                'description': 'Community Forum - Forum interaction edge cases',
                'focus': 'Content validation, spam protection, concurrent posting'
            },
            {
                'path': '/progress',
                'description': 'Progress Tracking - Progress management edge cases',
                'focus': 'Goal setting limits, achievement validation, data consistency'
            },
            {
                'path': '/profile',
                'description': 'User Profile - Profile management edge cases',
                'focus': 'File uploads, data validation, privacy settings'
            },
            {
                'path': '/career-paths',
                'description': 'Career Paths - Career exploration edge cases',
                'focus': 'Path recommendations, filtering, data accuracy'
            }
        ]
        
        # Run tests for each suite
        for i, suite in enumerate(test_suites, 1):
            print(f"\n📋 Test Suite {i}/{len(test_suites)}")
            print(f"🎯 Focus Areas: {suite['focus']}")
            self.run_test_suite(suite['path'], suite['description'])
            
            # Brief pause between tests
            time.sleep(2)
        
        # Generate comprehensive report
        self.generate_comprehensive_report()
    
    def generate_comprehensive_report(self):
        """Generate a comprehensive test report"""
        end_time = datetime.now()
        duration = end_time - self.start_time
        
        successful_tests = sum(1 for result in self.test_results if result['success'])
        failed_tests = len(self.test_results) - successful_tests
        success_rate = (successful_tests / len(self.test_results) * 100) if self.test_results else 0
        
        report = f"""
🎯 FAAFO Career Platform - Edge Case Testing Report
==================================================
📊 Test Summary:
   🕐 Duration: {duration}
   📋 Total Test Suites: {len(self.test_results)}
   ✅ Successful: {successful_tests}
   ❌ Failed: {failed_tests}
   📈 Success Rate: {success_rate:.1f}%

📋 Detailed Results:
"""
        
        for result in self.test_results:
            status_emoji = "✅" if result['success'] else "❌"
            report += f"""
{status_emoji} {result['description']}
   📍 Page: {result['page']}
   🕐 Time: {result['timestamp'].strftime('%H:%M:%S')}
   📝 Status: {'PASSED' if result['success'] else 'FAILED'}
"""
            if not result['success']:
                report += f"   ⚠️ Error: {result['output'][:200]}...\n"
        
        # Critical findings section
        report += f"""

🚨 Critical Findings Summary:
============================
"""
        
        critical_issues = []
        for result in self.test_results:
            if not result['success']:
                if 'injection' in result['output'].lower():
                    critical_issues.append(f"🔴 SQL Injection vulnerability detected on {result['page']}")
                if 'xss' in result['output'].lower():
                    critical_issues.append(f"🔴 XSS vulnerability detected on {result['page']}")
                if 'timeout' in result['output'].lower():
                    critical_issues.append(f"🟡 Performance issue on {result['page']}")
                if 'error' in result['output'].lower():
                    critical_issues.append(f"🟡 Error handling issue on {result['page']}")
        
        if critical_issues:
            for issue in critical_issues:
                report += f"   {issue}\n"
        else:
            report += "   🎉 No critical security vulnerabilities detected!\n"
        
        # Recommendations
        report += f"""

💡 Recommendations:
==================
   1. 🔒 Review all failed authentication tests for security gaps
   2. 🛡️ Implement additional input validation for edge cases
   3. ⚡ Optimize performance for pages with timeout issues
   4. 🧪 Add automated edge case testing to CI/CD pipeline
   5. 📊 Monitor error rates in production for these scenarios

📄 Individual test reports available in test-report-*.txt files
"""
        
        # Save comprehensive report
        report_filename = f"edge-case-testing-report-{int(time.time())}.txt"
        with open(report_filename, 'w') as f:
            f.write(report)
        
        print(report)
        print(f"\n📄 Comprehensive report saved to: {report_filename}")
        
        return {
            'total_suites': len(self.test_results),
            'successful': successful_tests,
            'failed': failed_tests,
            'success_rate': success_rate,
            'duration': duration,
            'critical_issues': len(critical_issues),
            'report_file': report_filename
        }

def main():
    """Main execution function"""
    print("🔍 Checking if development server is running...")
    
    # Check if server is running
    import requests
    try:
        response = requests.get("http://localhost:3000", timeout=5)
        print("✅ Development server is running")
    except:
        print("❌ Development server is not running!")
        print("Please start the server with: npm run dev")
        sys.exit(1)
    
    # Run comprehensive edge case testing
    runner = EdgeCaseTestRunner()
    results = runner.run_comprehensive_edge_case_testing()
    
    # Exit with appropriate code
    if results['failed'] > 0:
        print(f"\n⚠️ {results['failed']} test suites failed. Review the report for details.")
        sys.exit(1)
    else:
        print(f"\n🎉 All {results['successful']} test suites passed!")
        sys.exit(0)

if __name__ == "__main__":
    main()
