#!/usr/bin/env python3
"""
Focused Edge Case Testing for Critical Areas
Targets the most important security and functionality edge cases
"""

import subprocess
import time
import sys
from datetime import datetime

def run_focused_test(url, description, timeout=60):
    """Run a focused edge case test"""
    print(f"\n🎯 {description}")
    print(f"📍 {url}")
    print("-" * 50)
    
    cmd = ["python3", "ai_web_tester.py", url, f"Focused edge case: {description}"]
    
    try:
        start_time = time.time()
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=timeout)
        end_time = time.time()
        
        duration = end_time - start_time
        success = result.returncode == 0
        
        if success:
            print(f"✅ COMPLETED ({duration:.1f}s)")
            # Extract key findings from output
            output_lines = result.stdout.split('\n')
            failed_tests = [line for line in output_lines if '❌ FAILED:' in line]
            if failed_tests:
                print("⚠️ Issues found:")
                for issue in failed_tests[:3]:  # Show first 3 issues
                    print(f"   {issue.strip()}")
        else:
            print(f"❌ FAILED ({duration:.1f}s)")
            print(f"Error: {result.stderr[:200]}...")
            
        return success, duration, result.stdout if success else result.stderr
        
    except subprocess.TimeoutExpired:
        print(f"⏰ TIMEOUT ({timeout}s)")
        return False, timeout, "Test timed out"
    except Exception as e:
        print(f"💥 ERROR: {str(e)}")
        return False, 0, str(e)

def main():
    """Run focused edge case testing on critical areas"""
    
    print("🔍 FAAFO Career Platform - Focused Edge Case Testing")
    print("=" * 60)
    print("🎯 Targeting: Authentication, Input Validation, Session Security")
    print(f"🕐 Started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Check server
    import requests
    try:
        requests.get("http://localhost:3000", timeout=5)
        print("✅ Development server running")
    except:
        print("❌ Development server not running! Start with: npm run dev")
        sys.exit(1)
    
    base_url = "http://localhost:3000"
    
    # Critical edge case tests
    tests = [
        {
            'url': f'{base_url}/login',
            'description': 'Authentication Security - XSS, SQL Injection, Long Inputs',
            'timeout': 90
        },
        {
            'url': f'{base_url}/signup',
            'description': 'Registration Validation - Malformed Data, Boundary Testing',
            'timeout': 90
        },
        {
            'url': f'{base_url}/assessment',
            'description': 'Assessment Form Security - Concurrent Submissions, Data Integrity',
            'timeout': 90
        },
        {
            'url': f'{base_url}/profile',
            'description': 'Profile Management - File Upload Security, Input Sanitization',
            'timeout': 90
        },
        {
            'url': f'{base_url}/forum',
            'description': 'Forum Security - Content Validation, XSS Prevention',
            'timeout': 90
        }
    ]
    
    results = []
    total_start = time.time()
    
    for i, test in enumerate(tests, 1):
        print(f"\n📋 Test {i}/{len(tests)}")
        success, duration, output = run_focused_test(
            test['url'], 
            test['description'], 
            test['timeout']
        )
        
        results.append({
            'name': test['description'],
            'success': success,
            'duration': duration,
            'output': output
        })
        
        # Brief pause between tests
        time.sleep(2)
    
    total_duration = time.time() - total_start
    
    # Generate focused summary
    print(f"\n🎯 FOCUSED EDGE CASE TESTING SUMMARY")
    print("=" * 60)
    
    passed = sum(1 for r in results if r['success'])
    failed = len(results) - passed
    
    print(f"📊 Results:")
    print(f"   Total Tests: {len(results)}")
    print(f"   ✅ Passed: {passed}")
    print(f"   ❌ Failed: {failed}")
    print(f"   📈 Success Rate: {(passed/len(results)*100):.1f}%")
    print(f"   ⏱️ Total Duration: {total_duration:.1f}s")
    
    # Critical findings
    print(f"\n🚨 CRITICAL FINDINGS:")
    critical_issues = []
    
    for result in results:
        if not result['success']:
            output_lower = result['output'].lower()
            if any(keyword in output_lower for keyword in ['xss', 'injection', 'script']):
                critical_issues.append(f"🔴 {result['name']}: Security vulnerability detected")
            elif 'timeout' in output_lower:
                critical_issues.append(f"🟡 {result['name']}: Performance issue")
            else:
                critical_issues.append(f"🟡 {result['name']}: Functionality issue")
    
    if critical_issues:
        for issue in critical_issues:
            print(f"   {issue}")
    else:
        print("   🎉 No critical security issues detected in focused testing!")
    
    # Recommendations
    print(f"\n💡 IMMEDIATE RECOMMENDATIONS:")
    if failed > 0:
        print("   1. 🔒 Review and fix all failed security tests")
        print("   2. 🛡️ Implement input sanitization for all forms")
        print("   3. 🧪 Add automated security testing to CI/CD")
        print("   4. 📊 Monitor these edge cases in production")
    else:
        print("   1. ✅ All focused tests passed - good security posture")
        print("   2. 🔍 Continue regular edge case testing")
        print("   3. 📈 Consider expanding test coverage")
    
    # Save focused report
    timestamp = int(time.time())
    report_file = f"focused-edge-case-report-{timestamp}.txt"
    
    with open(report_file, 'w') as f:
        f.write(f"FAAFO Career Platform - Focused Edge Case Testing Report\n")
        f.write(f"Generated: {datetime.now()}\n")
        f.write(f"Duration: {total_duration:.1f}s\n")
        f.write(f"Tests: {len(results)}, Passed: {passed}, Failed: {failed}\n\n")
        
        for result in results:
            f.write(f"{result['name']}: {'PASSED' if result['success'] else 'FAILED'}\n")
            f.write(f"Duration: {result['duration']:.1f}s\n")
            if not result['success']:
                f.write(f"Issues: {result['output'][:500]}...\n")
            f.write("\n")
    
    print(f"\n📄 Detailed report saved to: {report_file}")
    
    # Exit with appropriate code
    return 0 if failed == 0 else 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
