
🤖 AI-Powered Web Testing Report
================================
🧠 AI Intelligence: 🧠 ENABLED
📊 Summary:
   Total Tests: 17
   ✅ Passed: 11
   ❌ Failed: 5
   🚨 Errors: 0
   ⏭️ Skipped: 1
   📈 Success Rate: 64.7%

📋 Detailed Results:
   ❌ page_load: FAILED
      Details: Title: '' | Issues: Missing page title

   ❌ accessibility: FAILED
      Details: No heading structure found

   ⏭️ forms: SKIPPED
      Details: No forms found

   ✅ navigation: PASSED
      Details: Tested 0 navigation links

   ✅ responsive_design: PASSED
      Details: Tested 3 viewport sizes
      Screenshot: test-screenshots/responsive_desktop_1749762009.png

   ✅ performance: PASSED
      Details: Load: 330ms, DOM: 325ms

   ✅ security_basics: PASSED
      Details: Basic security checks passed

   ✅ user_flows: PASSED
      Details: Tested 0 user flows

   ✅ ai_comprehensive_analysis: PASSED
      Details: AI analyzed 6 categories | Critical: 2 | Recommendations: 1 | Key findings: Usability: Forms may be missing CSRF protection; Security: Basic security checks recommended; Accessibility: Accessibility review needed
      Screenshot: test-screenshots/ai_analysis_1749762009.png

   ✅ edge_case_authentication: PASSED
      Details: Tested 0 auth edge cases | Issues: 0

   ✅ edge_case_assessment_system: PASSED
      Details: Tested 0 assessment edge cases | Issues: 0

   ✅ edge_case_data_validation: PASSED
      Details: Tested 0 validation edge cases | Issues: 0

   ❌ edge_case_error_handling: FAILED
      Details: Tested 3 error handling cases | Issues: 1 | Error handling test failed: Page.evaluate: Error: Test error
    at eval (eval at evaluate (:313:29), <anonymous>:1:7)
    at eval (<anonymous>)
    at UtilityScript.evaluate (<anonymous>:313:29)
    at UtilityScript.<anonymous> (<anonymous>:1:44)

   ❌ edge_case_boundary_conditions: FAILED
      Details: Tested 3 boundary conditions | Issues: 1 | Too few elements visible at very small viewport

   ✅ edge_case_concurrent_operations: PASSED
      Details: Tested 3 concurrent operations | Issues: 0

   ✅ edge_case_malformed_inputs: PASSED
      Details: Tested 0 malformed inputs | Issues: 0

   ❌ edge_case_session_management: FAILED
      Details: Tested 3 session management cases | Issues: 1 | Session ID may not be regenerated properly

